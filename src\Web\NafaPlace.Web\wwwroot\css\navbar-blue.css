:root {
    --primary: #E73C30;
    --secondary: #F96302;
    --dark: #003366;  /* Bleu foncé */
    --darker: #002244; /* Bleu plus foncé */
    --accent: #4D90FE; /* Bleu clair accent */
    --light: #f8f9fa;
    --white: #ffffff;
    --gray: #6c757d;
    --border: rgba(255, 255, 255, 0.15);
}

/* Header - Blue Style */
.header-blue {
    background-color: var(--dark);
    color: var(--white);
}

/* Top Bar */
.top-bar {
    background-color: var(--darker);
    padding: 8px 0;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    border-bottom: 1px solid var(--border);
}

.top-contact span {
    margin-right: 20px;
}

.top-contact i {
    margin-right: 5px;
    color: var(--accent);
}

.top-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    margin-left: 20px;
    transition: color 0.3s;
}

.top-links a:hover {
    color: var(--white);
}

/* Main Header */
.main-header {
    display: flex;
    align-items: center;
    padding: 15px 0;
}

/* Logo */
.logo {
    font-size: 1.8rem;
    font-weight: 700;
    text-decoration: none;
    color: var(--white);
}

.logo-nafa {
    color: var(--primary);
}

.logo-place {
    color: var(--secondary);
}

/* Search Form */
.search-wrapper {
    flex-grow: 1;
    margin: 0 20px;
    display: flex;
}

.search-form {
    display: flex;
    align-items: center;
    width: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 30px;
    overflow: hidden;
    transition: all 0.3s;
}

.search-form:focus-within {
    background-color: rgba(255, 255, 255, 0.15);
}

.search-category {
    background-color: rgba(0, 0, 0, 0.2);
    border: none;
    color: var(--white);
    font-size: 0.9rem;
    padding: 12px 20px;
    border-radius: 30px 0 0 30px;
    cursor: pointer;
    min-width: 140px;
    outline: none;
}

.search-category option {
    background-color: var(--dark);
    color: var(--white);
}

.search-input {
    flex-grow: 1;
    border: none;
    background-color: transparent;
    padding: 12px 20px;
    color: var(--white);
    outline: none;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.search-btn {
    background-color: var(--secondary);
    border: none;
    color: var(--white);
    padding: 12px 25px;
    font-weight: 500;
    cursor: pointer;
    border-radius: 0 30px 30px 0;
    transition: background-color 0.3s;
}

.search-btn:hover {
    background-color: var(--primary);
}

/* User Actions */
.user-actions {
    display: flex;
    align-items: center;
}

.action-item {
    margin-left: 15px;
    text-decoration: none;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.action-icon {
    font-size: 1.4rem;
    color: var(--white);
    transition: color 0.3s;
}

.action-label {
    font-size: 0.7rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 3px;
    transition: color 0.3s;
}

.action-badge {
    position: absolute;
    top: -5px;
    right: -8px;
    background-color: var(--primary);
    color: var(--white);
    font-size: 0.65rem;
    font-weight: 600;
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 9px;
    padding: 0 5px;
}

.action-item:hover .action-icon,
.action-item:hover .action-label {
    color: var(--accent);
}

/* Categories Button */
.categories-button {
    position: relative;
    background-color: var(--primary);
    color: var(--white);
    font-weight: 600;
    padding: 15px 25px;
    display: flex;
    align-items: center;
    border: none;
    cursor: pointer;
    z-index: 1052;
}

.categories-button i {
    margin-right: 10px;
}

/* Products Button */
.products-button {
    position: relative;
    background-color: var(--secondary);
    color: var(--white);
    font-weight: 600;
    padding: 15px 25px;
    display: flex;
    align-items: center;
    border: none;
    cursor: pointer;
    z-index: 1052;
    margin-left: 10px;
}

.products-button i {
    margin-right: 10px;
}

/* Dropdown Menus */
.dropdown-menu-custom {
    position: absolute;
    top: 100%;
    left: 0;
    width: 270px;
    background-color: var(--white);
    border-radius: 0 0 5px 5px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: none;
    z-index: 1000;
}

/* Bootstrap Dropdown Fixes */
.navbar-blue .dropdown {
    position: relative;
    z-index: 1050;
}

.navbar-blue .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1051 !important;
    min-width: 250px;
    background-color: var(--white);
    border: none;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    margin-top: 0;
    padding: 0.5rem 0;
}

.navbar-blue .dropdown-item {
    padding: 0.75rem 1.25rem;
    color: var(--dark);
    transition: all 0.3s ease;
    border: none;
    background: none;
}

.navbar-blue .dropdown-item:hover,
.navbar-blue .dropdown-item:focus {
    background-color: rgba(231, 60, 48, 0.05);
    color: var(--primary);
    padding-left: 1.5rem;
}

.navbar-blue .dropdown-item i {
    color: var(--secondary);
    margin-right: 0.75rem;
    width: 16px;
    text-align: center;
}

.navbar-blue .dropdown-divider {
    margin: 0.5rem 0;
    border-color: rgba(0, 0, 0, 0.1);
}

/* Ensure main content is below dropdowns */
.page {
    position: relative;
    z-index: 1;
}

main {
    position: relative;
    z-index: 1;
}

/* Ensure hero sections and content sections are below dropdowns */
.hero-section,
.bestsellers-section,
.promotions-section,
.featured-section,
.content-section {
    position: relative;
    z-index: 1;
}

.categories-button:hover + .categories-dropdown,
.categories-dropdown:hover {
    display: block;
}

.products-button:hover + .products-dropdown,
.products-dropdown:hover {
    display: block;
}

.menu-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.menu-item {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.menu-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--dark);
    text-decoration: none;
    transition: all 0.3s;
}

.menu-link:hover {
    background-color: rgba(0, 0, 0, 0.03);
    color: var(--primary);
    padding-left: 25px;
}

.menu-icon {
    margin-right: 15px;
    color: var(--secondary);
    font-size: 1.1rem;
}

/* Navigation Bar */
.navbar-blue {
    background-color: var(--dark);
    padding: 0;
    border-top: 1px solid var(--border);
    position: relative;
    z-index: 1050;
}

.navbar-container {
    display: flex;
    align-items: center;
    position: relative;
}

/* Main Navigation */
.main-nav {
    display: flex;
    margin-left: 20px;
    list-style: none;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: var(--white);
    padding: 15px 20px !important;
    font-weight: 500;
    transition: color 0.3s;
    text-decoration: none;
    display: block;
}

.nav-link:hover,
.nav-link.active {
    color: var(--accent);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 20px;
    right: 20px;
    height: 3px;
    background-color: var(--accent);
}

/* Hotline */
.hotline {
    margin-left: auto;
    display: flex;
    align-items: center;
    padding: 15px 0;
}

.hotline-icon {
    font-size: 1.3rem;
    color: var(--primary);
    margin-right: 10px;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.hotline-content {
    display: flex;
    flex-direction: column;
}

.hotline-label {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
}

.hotline-number {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--white);
}

/* Mobile Menu Button */
.menu-btn {
    display: none;
    background: transparent;
    border: none;
    color: var(--white);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    margin-right: 15px;
}

/* Mobile Navigation */
.offcanvas {
    background-color: var(--dark);
    color: var(--white);
}

.offcanvas-header {
    background-color: var(--primary);
    padding: 1rem 1.5rem;
}

.offcanvas-title {
    font-weight: 600;
}

.mobile-search {
    padding: 15px;
    position: relative;
}

.mobile-search-input {
    width: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 25px;
    padding: 12px 20px 12px 45px;
    color: var(--white);
}

.mobile-search-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.mobile-search-icon {
    position: absolute;
    left: 30px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.5);
}

.mobile-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mobile-nav-item {
    border-bottom: 1px solid var(--border);
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: var(--white);
    text-decoration: none;
    transition: all 0.3s;
}

.mobile-nav-link:hover {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--accent);
}

.mobile-nav-icon {
    margin-right: 15px;
    color: var(--accent);
    font-size: 1.1rem;
}

.mobile-account-btn {
    display: block;
    margin: 15px;
    padding: 12px;
    background-color: var(--secondary);
    color: var(--white);
    text-align: center;
    font-weight: 600;
    border-radius: 25px;
    text-decoration: none;
    transition: background-color 0.3s;
}

.mobile-account-btn:hover {
    background-color: var(--primary);
    color: var(--white);
}

/* Promotional Banner - Compact Orange Style */
.promo-banner {
    background: linear-gradient(135deg, #FF6B35 0%, #F59E0B 100%) !important;
    color: white !important;
    padding: 20px 0 !important;
    margin: 30px 0 !important;
    position: relative;
    overflow: hidden;
    border-radius: 0 !important;
    min-height: auto !important;
}

.promo-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.promo-banner h3 {
    font-weight: 700 !important;
    margin-bottom: 8px !important;
    font-size: 1.3rem !important;
    position: relative;
    z-index: 2;
    color: white !important;
}

.promo-banner p {
    margin-bottom: 12px !important;
    opacity: 0.95;
    font-size: 0.95rem !important;
    position: relative;
    z-index: 2;
    color: white !important;
}

.promo-banner .btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.8) !important;
    color: white !important;
    padding: 8px 16px !important;
    font-weight: 600;
    border-radius: 20px !important;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 2;
    font-size: 0.85rem !important;
}

.promo-banner .btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: white !important;
    color: white !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
}

.promo-banner .bi-truck {
    color: rgba(255, 255, 255, 0.2) !important;
    font-size: 2.5rem !important;
}

/* Responsive pour la bannière promotionnelle */
@media (max-width: 768px) {
    .promo-banner {
        padding: 15px 0 !important;
        margin: 20px 0 !important;
    }

    .promo-banner h3 {
        font-size: 1.1rem !important;
        margin-bottom: 6px !important;
    }

    .promo-banner p {
        font-size: 0.85rem !important;
        margin-bottom: 10px !important;
    }

    .promo-banner .btn-outline-light {
        padding: 6px 12px !important;
        font-size: 0.8rem !important;
    }

    .promo-banner .bi-truck {
        font-size: 2rem !important;
    }
}

/* Responsive */
@media (max-width: 991.98px) {
    .top-bar {
        display: none;
    }

    .main-header {
        padding: 15px 0;
    }

    .search-wrapper {
        display: none;
    }

    .menu-btn {
        display: block;
    }

    .user-actions {
        margin-left: auto;
    }

    .action-item {
        margin-left: 15px;
    }

    .action-label {
        display: none;
    }

    .navbar-blue {
        display: none;
    }
}

/* Améliorations pour mobile */
@media (max-width: 768px) {
    .main-header {
        padding: 10px 0;
    }

    .logo img {
        height: 60px;
    }

    .action-item {
        margin-left: 10px;
    }

    .action-icon {
        font-size: 1.2rem;
    }

    .action-badge {
        font-size: 0.7rem;
        min-width: 16px;
        height: 16px;
        line-height: 16px;
    }
}

/* Menu mobile amélioré */
.offcanvas-body {
    padding: 0;
}

.mobile-search {
    padding: 1rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.mobile-search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #ced4da;
    border-radius: 25px;
    font-size: 0.95rem;
}

.mobile-search-icon {
    position: absolute;
    left: 1.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 10;
}

.mobile-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mobile-nav-item {
    border-bottom: 1px solid #f1f3f4;
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    color: #333;
    text-decoration: none;
    font-size: 0.95rem;
    transition: all 0.2s ease;
}

.mobile-nav-link:hover {
    background-color: #f8f9fa;
    color: var(--primary);
}

.mobile-nav-icon {
    margin-right: 0.75rem;
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

/* Amélioration des boutons d'action mobile */
@media (max-width: 576px) {
    .logo img {
        height: 50px;
    }

    .action-item {
        margin-left: 8px;
    }

    .action-icon {
        font-size: 1.1rem;
    }

    .mobile-nav-link {
        padding: 0.875rem 1.25rem;
        font-size: 0.9rem;
    }

    .mobile-search-input {
        padding: 0.625rem 0.875rem 0.625rem 2.25rem;
        font-size: 0.9rem;
    }

    .mobile-search-icon {
        left: 1.5rem;
    }
}
