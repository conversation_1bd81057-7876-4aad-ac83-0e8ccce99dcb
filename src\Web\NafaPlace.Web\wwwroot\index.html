<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>NafaPlace - Votre marketplace africaine</title>
    <meta name="description" content="NafaPlace est une marketplace africaine proposant des produits de qualité, des vendeurs vérifiés et une expérience d'achat sécurisée." />
    <base href="/" />
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="images/nafaplace-gradient-modern.svg" />
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65" crossorigin="anonymous">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" referrerpolicy="no-referrer" />
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="css/app.css" rel="stylesheet" />
    <link href="css/nafaplace.css" rel="stylesheet" />
    <link href="css/navbar-blue.css" rel="stylesheet" />
    <link href="css/catalog-responsive.css" rel="stylesheet" />
    <link href="css/gradients-harmonized.css" rel="stylesheet" />
    
    <!-- Blazor WebAssembly CSS -->
    <link href="NafaPlace.Web.styles.css" rel="stylesheet" />
    
    <!-- PWA support -->
    <link href="manifest.json" rel="manifest" />
    <link rel="apple-touch-icon" sizes="512x512" href="icon-512.png" />
    <link rel="apple-touch-icon" sizes="192x192" href="icon-192.png" />
</head>

<body>
    <div id="app">
        <div class="loading-container">
            <div class="loading-logo">
                <img src="images/nafaplace-gradient-modern.svg" alt="NafaPlace Logo" height="80" />
            </div>
            <div class="loading-spinner">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                </div>
            </div>
            <div class="loading-text">
                Chargement de NafaPlace...
            </div>
        </div>
    </div>

    <div id="blazor-error-ui" class="alert alert-danger" role="alert" style="display: none;">
        Une erreur s'est produite.
        <a href="" class="reload">Recharger</a>
        <a class="dismiss">🗙</a>
    </div>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-kenU1KFdBIe4zVF0s0G1M5b4hcpxyD9F7jL+jjXkk+Q2h455rYXK/7HAuoJl+0I4" crossorigin="anonymous"></script>
    
    <!-- Blazor WebAssembly JS -->
    <script src="_framework/blazor.webassembly.js"></script>
    
    <!-- Custom JS -->
    <script src="js/app.js"></script>

    <!-- Stripe Payment Functions -->
    <script>
        // Fonction pour initialiser Stripe
        window.initializeStripe = function() {
            // Ici on peut ajouter l'initialisation de Stripe Elements si nécessaire
            return true;
        };

        // Fonction pour créer une session Stripe Checkout
        window.createStripeCheckoutSession = async function(checkoutRequest) {
            try {
                const response = await fetch('http://localhost:5005/api/Payments/create-checkout-session', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(checkoutRequest)
                });

                if (response.ok) {
                    const result = await response.json();
                    return result.sessionUrl;
                } else {
                    return null;
                }
            } catch (error) {
                console.error('Network error creating Stripe session:', error);
                return null;
            }
        };
    </script>

    <!-- PWA support disabled temporarily to fix .wasm loading issues -->
    <!-- <script>
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('service-worker.js');
        }
    </script> -->
</body>

</html>
