@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Routing
@inject NavigationManager NavigationManager

<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="">NafaPlace Admin</a>
        <button title="Navigation menu" class="navbar-toggler" @onclick="ToggleNavMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
    </div>
</div>

<div class="@NavMenuCssClass nav-scrollable" @onclick="ToggleNavMenu">
    <nav class="nav flex-column">
        <!-- Accueil -->
        <div class="nav-item px-3">
            <a class="nav-link @(GetActive(""))" href="">
                <span class="bi bi-house-door-fill-nav-menu" aria-hidden="true"></span> Accueil
            </a>
        </div>

        <!-- Tableau de bord -->
        <div class="nav-item px-3">
            <a class="nav-link @(GetActive("dashboard"))" href="dashboard">
                <span class="fas fa-tachometer-alt" aria-hidden="true"></span> Tableau de bord
            </a>
        </div>

        <!-- Section Gestion des utilisateurs -->
        <div class="nav-item px-3 mt-3">
            <div class="nav-section-header" @onclick="ToggleUsersSection">
                <span class="fas fa-users me-2" aria-hidden="true"></span>
                <span>Gestion des utilisateurs</span>
                <span class="fas @(showUsersSection ? "fa-chevron-down" : "fa-chevron-right") ms-auto" aria-hidden="true"></span>
            </div>
        </div>
        <div class="nav-subsection @(showUsersSection ? "show" : "")">
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("users"))" href="users">
                    <span class="fas fa-user" aria-hidden="true"></span> Utilisateurs
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("roles"))" href="roles">
                    <span class="fas fa-user-tag" aria-hidden="true"></span> Rôles
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("sellers"))" href="sellers">
                    <span class="fas fa-store" aria-hidden="true"></span> Vendeurs
                </a>
            </div>
        </div>

        <!-- Section Catalogue -->
        <div class="nav-item px-3 mt-2">
            <div class="nav-section-header" @onclick="ToggleCatalogSection">
                <span class="fas fa-box me-2" aria-hidden="true"></span>
                <span>Catalogue</span>
                <span class="fas @(showCatalogSection ? "fa-chevron-down" : "fa-chevron-right") ms-auto" aria-hidden="true"></span>
            </div>
        </div>
        <div class="nav-subsection @(showCatalogSection ? "show" : "")">
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("products"))" href="products">
                    <span class="fas fa-box" aria-hidden="true"></span> Produits
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("categories"))" href="categories">
                    <span class="fas fa-tags" aria-hidden="true"></span> Catégories
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("inventory"))" href="inventory">
                    <span class="fas fa-warehouse" aria-hidden="true"></span> Inventaire
                </a>
            </div>
        </div>

        <!-- Section Commandes & Marketing -->
        <div class="nav-item px-3 mt-2">
            <div class="nav-section-header" @onclick="ToggleOrdersSection">
                <span class="fas fa-shopping-cart me-2" aria-hidden="true"></span>
                <span>Commandes & Marketing</span>
                <span class="fas @(showOrdersSection ? "fa-chevron-down" : "fa-chevron-right") ms-auto" aria-hidden="true"></span>
            </div>
        </div>
        <div class="nav-subsection @(showOrdersSection ? "show" : "")">
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("orders"))" href="orders">
                    <span class="fas fa-shopping-cart" aria-hidden="true"></span> Commandes
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("reviews"))" href="reviews">
                    <span class="fas fa-star" aria-hidden="true"></span> Avis
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("coupons"))" href="coupons">
                    <span class="fas fa-ticket-alt" aria-hidden="true"></span> Coupons
                </a>
            </div>
        </div>

        <!-- Section Livraison -->
        <div class="nav-item px-3 mt-2">
            <div class="nav-section-header" @onclick="ToggleDeliverySection">
                <span class="fas fa-truck me-2" aria-hidden="true"></span>
                <span>Livraison</span>
                <span class="fas @(showDeliverySection ? "fa-chevron-down" : "fa-chevron-right") ms-auto" aria-hidden="true"></span>
            </div>
        </div>
        <div class="nav-subsection @(showDeliverySection ? "show" : "")">
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("delivery/zones"))" href="delivery/zones">
                    <span class="fas fa-map-marked-alt" aria-hidden="true"></span> Zones de Livraison
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("delivery/carriers"))" href="delivery/carriers">
                    <span class="fas fa-truck" aria-hidden="true"></span> Transporteurs
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("delivery/tracking"))" href="delivery/tracking">
                    <span class="fas fa-route" aria-hidden="true"></span> Suivi des Livraisons
                </a>
            </div>
            <div class="nav-item px-4">
                <a class="nav-link @(GetActive("delivery/calculator"))" href="delivery/calculator">
                    <span class="fas fa-calculator" aria-hidden="true"></span> Calculateur de Frais
                </a>
            </div>
        </div>
    </nav>
</div>

@code {
    private bool collapseNavMenu = true;
    private bool showUsersSection = false;
    private bool showCatalogSection = false;
    private bool showOrdersSection = false;
    private bool showDeliverySection = false;

    private string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    protected override void OnInitialized()
    {
        // Auto-expand sections based on current route
        var uri = new Uri(NavigationManager.Uri);
        var path = uri.AbsolutePath;

        if (path.Contains("/users") || path.Contains("/roles") || path.Contains("/sellers"))
            showUsersSection = true;
        else if (path.Contains("/products") || path.Contains("/categories") || path.Contains("/inventory"))
            showCatalogSection = true;
        else if (path.Contains("/orders") || path.Contains("/reviews") || path.Contains("/coupons"))
            showOrdersSection = true;
        else if (path.Contains("/delivery"))
            showDeliverySection = true;
    }

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }

    private void ToggleUsersSection()
    {
        showUsersSection = !showUsersSection;
    }

    private void ToggleCatalogSection()
    {
        showCatalogSection = !showCatalogSection;
    }

    private void ToggleOrdersSection()
    {
        showOrdersSection = !showOrdersSection;
    }

    private void ToggleDeliverySection()
    {
        showDeliverySection = !showDeliverySection;
    }

    private string GetActive(string href)
    {
        var uri = new Uri(NavigationManager.Uri);
        var path = uri.AbsolutePath;

        if (string.IsNullOrEmpty(href) && path == "/")
            return "active";

        if (!string.IsNullOrEmpty(href) && path.StartsWith($"/{href}"))
            return "active";

        return "";
    }
}
