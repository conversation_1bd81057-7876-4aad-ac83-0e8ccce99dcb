// app.js - Script principal pour NafaPlace

// Initialisation lorsque le document est chargé
document.addEventListener('DOMContentLoaded', function() {
    initializeTooltips();
    initializeDropdowns();
});

// Fonction appelée après que Blazor est complètement chargé
window.initializeApp = function() {
    initializeTooltips();
    initializeDropdowns();
};

// Fonction pour créer une session Stripe Checkout
window.createStripeCheckoutSession = async function(checkoutRequest) {
    try {


        const response = await fetch('http://localhost:5005/api/Payments/create-checkout-session', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(checkoutRequest)
        });

        if (response.ok) {
            const result = await response.json();

            return result.sessionUrl;
        } else {
            console.error('Error creating Stripe session:', response.status, response.statusText);
            const errorText = await response.text();
            console.error('Error details:', errorText);
            return null;
        }
    } catch (error) {
        console.error('Network error creating Stripe session:', error);
        return null;
    }
};

// Initialiser les tooltips Bootstrap
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Initialiser les dropdowns Bootstrap
function initializeDropdowns() {
    var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
    dropdownElementList.map(function(dropdownToggleEl) {
        return new bootstrap.Dropdown(dropdownToggleEl);
    });
}

// Fonction pour afficher un message toast avec protection contre les appels répétitifs
window.showToast = function(message, type = 'info', duration = 3000) {
    // Éviter les toasts en double
    if (window.lastToastMessage === message && Date.now() - (window.lastToastTime || 0) < 1000) {
        return;
    }
    window.lastToastMessage = message;
    window.lastToastTime = Date.now();
    // Créer l'élément toast
    var toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    // Créer le toast
    var toastId = 'toast-' + Date.now();
    var toast = document.createElement('div');
    toast.id = toastId;
    toast.className = 'toast align-items-center text-white bg-' + type;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    // Contenu du toast
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;

    // Ajouter le toast au conteneur
    toastContainer.appendChild(toast);

    // Initialiser et afficher le toast
    var toastElement = new bootstrap.Toast(toast, {
        autohide: true,
        delay: duration
    });
    toastElement.show();

    // Supprimer le toast après qu'il soit caché
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
};

// Fonction pour copier du texte dans le presse-papier
window.copyToClipboard = function(text) {
    navigator.clipboard.writeText(text).then(function() {
        window.showToast('Copié dans le presse-papier!', 'success', 2000);
    }).catch(function(err) {
        window.showToast('Erreur lors de la copie: ' + err, 'danger', 3000);
    });
};

// Fonction pour gérer le mode sombre
window.toggleDarkMode = function(isDark) {
    if (isDark) {
        document.body.classList.add('dark-mode');
        localStorage.setItem('darkMode', 'true');
    } else {
        document.body.classList.remove('dark-mode');
        localStorage.setItem('darkMode', 'false');
    }
};

// Vérifier si le mode sombre est activé au chargement
(function() {
    var isDarkMode = localStorage.getItem('darkMode') === 'true';
    window.toggleDarkMode(isDarkMode);
})();

// Fonction pour détecter la connexion internet
window.checkOnlineStatus = function() {
    return navigator.onLine;
};

// Écouter les changements de statut de connexion
window.addEventListener('online', function() {
    window.showToast('Vous êtes de nouveau connecté!', 'success');
});

window.addEventListener('offline', function() {
    window.showToast('Vous êtes hors ligne. Certaines fonctionnalités peuvent être limitées.', 'warning');
});
